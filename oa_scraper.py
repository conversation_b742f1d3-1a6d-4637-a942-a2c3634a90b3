#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA数据爬虫程序
抓取http://wechat.stu.edu.cn/oa/OA_list.html的数据
目标：抓取1万条记录
使用Selenium处理JavaScript动态加载的内容
"""

import csv
import time
import json
import logging
import os
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from bs4 import BeautifulSoup

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OAScraper:
    def __init__(self):
        self.base_url = "http://wechat.stu.edu.cn/oa/OA_list.html"
        self.token = "94cw0IjFHPvqCb/OHlgtJT3yU09jZw1zvb7R/nQhTwA="

        # 设置Chrome选项
        self.chrome_options = Options()
        self.chrome_options.add_argument('--headless')  # 无头模式
        self.chrome_options.add_argument('--no-sandbox')
        self.chrome_options.add_argument('--disable-dev-shm-usage')
        self.chrome_options.add_argument('--disable-gpu')
        self.chrome_options.add_argument('--window-size=1920,1080')
        self.chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36')

        self.driver = None
        self.records = []
        self.total_target = 10000  # 目标抓取1万条记录
        self.records_per_page = 10  # 每页10条记录
        self.delay = 2  # 请求间隔（秒）
        self.page_load_timeout = 30  # 页面加载超时时间
        
    def init_driver(self):
        """初始化WebDriver"""
        try:
            self.driver = webdriver.Chrome(options=self.chrome_options)
            self.driver.set_page_load_timeout(self.page_load_timeout)
            logging.info("WebDriver初始化成功")
            return True
        except Exception as e:
            logging.error(f"WebDriver初始化失败：{e}")
            return False

    def close_driver(self):
        """关闭WebDriver"""
        if self.driver:
            self.driver.quit()
            logging.info("WebDriver已关闭")

    def get_page_data(self, page_no):
        """获取指定页面的数据"""
        if not self.driver:
            if not self.init_driver():
                return None

        try:
            # 构建URL
            url = f"{self.base_url}?TokenOa={self.token}&PageContainsRecord={self.records_per_page}&CurrentPageNo={page_no}"

            logging.info(f"正在访问第{page_no}页：{url}")
            self.driver.get(url)

            # 等待页面加载完成，等待doc_ul元素出现
            wait = WebDriverWait(self.driver, 15)

            # 等待页面基本结构加载
            wait.until(EC.presence_of_element_located((By.ID, "doc_ul")))

            # 额外等待JavaScript执行完成
            time.sleep(3)

            # 检查是否有数据加载
            doc_ul = self.driver.find_element(By.ID, "doc_ul")

            # 等待数据加载（检查ul中是否有li元素）
            try:
                wait.until(lambda driver: len(doc_ul.find_elements(By.TAG_NAME, "li")) > 0)
                logging.info(f"第{page_no}页数据加载完成")
            except TimeoutException:
                logging.warning(f"第{page_no}页可能没有数据或数据加载超时")

            return self.driver.page_source

        except TimeoutException:
            logging.error(f"第{page_no}页加载超时")
            return None
        except Exception as e:
            logging.error(f"获取第{page_no}页数据失败：{e}")
            return None
    
    def parse_page_data(self, html_content, page_no):
        """解析页面数据，重点关注doc_ul中的内容"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            page_records = []

            # 首先尝试从doc_ul中提取数据
            doc_ul = soup.find('ul', id='doc_ul')
            if doc_ul:
                li_elements = doc_ul.find_all('li')
                logging.info(f"第{page_no}页在doc_ul中找到{len(li_elements)}个li元素")

                for i, li in enumerate(li_elements):
                    text_content = li.get_text(strip=True)
                    if text_content and len(text_content) > 5:  # 过滤掉太短的内容
                        # 尝试提取更多信息
                        links = li.find_all('a')
                        link_info = []
                        for link in links:
                            href = link.get('href', '')
                            link_text = link.get_text(strip=True)
                            if href and link_text:
                                link_info.append({
                                    'text': link_text,
                                    'href': href
                                })

                        record = {
                            'page': page_no,
                            'item_index': i + 1,
                            'content': text_content,
                            'links': link_info,
                            'html': str(li),
                            'timestamp': datetime.now().isoformat()
                        }
                        page_records.append(record)

                if page_records:
                    logging.info(f"第{page_no}页从doc_ul解析到{len(page_records)}条有效记录")
                    return page_records

            # 如果doc_ul中没有数据，尝试其他方法
            logging.warning(f"第{page_no}页doc_ul中没有找到数据，尝试其他解析方法")

            # 查找所有可能包含数据的元素
            potential_containers = soup.find_all(['div', 'table', 'ul', 'ol'],
                                                class_=lambda x: x and ('list' in x.lower() or 'item' in x.lower() or 'record' in x.lower()))

            for container in potential_containers:
                items = container.find_all(['li', 'tr', 'div'])
                for i, item in enumerate(items):
                    text_content = item.get_text(strip=True)
                    if text_content and len(text_content) > 10:
                        record = {
                            'page': page_no,
                            'item_index': i + 1,
                            'content': text_content,
                            'links': [],
                            'html': str(item),
                            'timestamp': datetime.now().isoformat()
                        }
                        page_records.append(record)

            # 如果还是没有找到数据，记录页面的基本信息用于调试
            if not page_records:
                logging.warning(f"第{page_no}页没有找到任何有效数据，保存页面信息用于调试")
                record = {
                    'page': page_no,
                    'item_index': 1,
                    'content': f"页面标题: {soup.title.get_text() if soup.title else '无标题'}",
                    'links': [],
                    'html': html_content[:1000] + "..." if len(html_content) > 1000 else html_content,
                    'timestamp': datetime.now().isoformat(),
                    'debug_info': {
                        'doc_ul_exists': bool(doc_ul),
                        'doc_ul_children': len(doc_ul.find_all()) if doc_ul else 0,
                        'page_length': len(html_content)
                    }
                }
                page_records.append(record)

            logging.info(f"第{page_no}页最终解析到{len(page_records)}条记录")
            return page_records

        except Exception as e:
            logging.error(f"解析第{page_no}页数据失败：{e}")
            return []
    
    def save_to_csv(self, filename='oa_data.csv'):
        """保存数据到CSV文件"""
        if not self.records:
            logging.warning("没有数据可保存")
            return
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                fieldnames = ['page', 'content', 'html', 'timestamp']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for record in self.records:
                    writer.writerow(record)
            
            logging.info(f"数据已保存到 {filename}，共{len(self.records)}条记录")
            
        except Exception as e:
            logging.error(f"保存CSV文件失败：{e}")
    
    def save_to_json(self, filename='oa_data.json'):
        """保存数据到JSON文件"""
        if not self.records:
            logging.warning("没有数据可保存")
            return
        
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.records, jsonfile, ensure_ascii=False, indent=2)
            
            logging.info(f"数据已保存到 {filename}，共{len(self.records)}条记录")
            
        except Exception as e:
            logging.error(f"保存JSON文件失败：{e}")
    
    def scrape(self):
        """主要的爬取方法"""
        logging.info(f"开始爬取数据，目标：{self.total_target}条记录")
        
        page_no = 1
        consecutive_failures = 0
        max_consecutive_failures = 5
        
        while len(self.records) < self.total_target:
            logging.info(f"正在爬取第{page_no}页...")
            
            # 获取页面数据
            html_content = self.get_page_data(page_no)
            
            if html_content is None:
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    logging.error(f"连续{max_consecutive_failures}次失败，停止爬取")
                    break
                
                logging.warning(f"第{page_no}页获取失败，等待{self.delay * 2}秒后重试...")
                time.sleep(self.delay * 2)
                continue
            
            # 重置失败计数
            consecutive_failures = 0
            
            # 解析页面数据
            page_records = self.parse_page_data(html_content, page_no)
            
            if not page_records:
                logging.warning(f"第{page_no}页没有解析到数据，可能已到达最后一页")
                break
            
            # 添加到总记录中
            self.records.extend(page_records)
            
            logging.info(f"已爬取{len(self.records)}条记录，进度：{len(self.records)/self.total_target*100:.1f}%")
            
            # 每100页保存一次数据（防止数据丢失）
            if page_no % 100 == 0:
                self.save_to_csv(f'oa_data_backup_{page_no}.csv')
                self.save_to_json(f'oa_data_backup_{page_no}.json')
            
            page_no += 1
            
            # 延时避免被封
            time.sleep(self.delay)
        
        # 最终保存
        self.save_to_csv()
        self.save_to_json()
        
        logging.info(f"爬取完成！总共获取{len(self.records)}条记录")

def main():
    """主函数"""
    scraper = OAScraper()
    
    try:
        scraper.scrape()
    except KeyboardInterrupt:
        logging.info("用户中断爬取")
        # 保存已获取的数据
        scraper.save_to_csv('oa_data_interrupted.csv')
        scraper.save_to_json('oa_data_interrupted.json')
    except Exception as e:
        logging.error(f"爬取过程中发生错误：{e}")
        # 保存已获取的数据
        scraper.save_to_csv('oa_data_error.csv')
        scraper.save_to_json('oa_data_error.json')

if __name__ == "__main__":
    main()
