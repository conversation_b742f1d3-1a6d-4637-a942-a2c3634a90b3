#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OA数据爬虫程序
抓取汕头大学OA系统数据
使用发现的API端点：http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc
目标：抓取1万条记录
"""

import requests
import csv
import time
import json
import logging
from datetime import datetime
from urllib.parse import quote

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

class OAScraper:
    def __init__(self):
        self.api_url = "http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDoc"
        self.doc_num_url = "http://wechat.stu.edu.cn/webservice_oa/oa_stu_/GetDocNum"
        self.token = "94cw0IjFHPvqCb/OHlgtJT3yU09b7R/nQhTwA="
        self.token_encoded = quote(self.token)
        self.referrer = f"http://wechat.stu.edu.cn/oa/OA_list.html?TokenOa={self.token_encoded}&PageContainsRecord=10&CurrentPageNo=1"

        # 设置requests session
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh-TW;q=0.7,zh;q=0.6,ja;q=0.5',
            'Cache-Control': 'no-cache',
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            'Pragma': 'no-cache',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': self.referrer,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

        self.records = []
        self.total_target = 1000  # 目标抓取1万条记录
        self.records_per_batch = 100  # 每次请求的记录数
        self.delay = 1  # 请求间隔（秒）
        self.total_docs = None  # 总文档数
        
    def get_doc_num(self):
        """获取文档总数"""
        try:
            # 准备POST数据，使用URL编码的token
            data = {
                'token': self.token_encoded,
                'subcompany_id': '0',
                'keyword': ''
            }

            logging.info(f"正在获取文档总数...")
            logging.info(f"请求URL: {self.doc_num_url}")
            logging.info(f"请求数据: {data}")

            response = self.session.post(self.doc_num_url, data=data, timeout=30)
            response.raise_for_status()

            logging.info(f"GetDocNum响应状态码: {response.status_code}")
            logging.info(f"GetDocNum响应内容: {response.text}")

            # 尝试解析响应
            try:
                result = response.json()
                if isinstance(result, dict) and 'total' in result:
                    self.total_docs = int(result['total'])
                elif response.text.isdigit():
                    self.total_docs = int(response.text)
                else:
                    logging.warning(f"无法解析文档总数: {response.text}")
                    self.total_docs = None

                logging.info(f"文档总数: {self.total_docs}")
                return self.total_docs

            except (json.JSONDecodeError, ValueError) as e:
                logging.error(f"解析文档总数失败: {e}")
                return None

        except requests.exceptions.RequestException as e:
            logging.error(f"获取文档总数失败：{e}")
            return None
        except Exception as e:
            logging.error(f"处理文档总数请求时发生错误：{e}")
            return None

    def get_batch_data(self, row_start, row_end):
        """获取指定范围的数据"""
        try:
            # 准备POST数据，使用URL编码的token
            data = {
                'token': self.token_encoded,
                'subcompany_id': '0',
                'keyword': '',
                'row_start': str(row_start),
                'row_end': str(row_end)
            }

            logging.info(f"正在获取第{row_start}-{row_end}条记录...")
            logging.info(f"请求URL: {self.api_url}")
            logging.info(f"请求数据: {data}")

            response = self.session.post(self.api_url, data=data, timeout=30)
            response.raise_for_status()

            logging.info(f"响应状态码: {response.status_code}")
            logging.info(f"响应头: {dict(response.headers)}")
            logging.info(f"响应内容长度: {len(response.text)}")
            logging.info(f"响应内容前500字符: {response.text[:500]}")

            # 尝试解析JSON响应
            try:
                json_data = response.json()
                logging.info(f"成功解析JSON响应")
                return json_data
            except json.JSONDecodeError:
                logging.warning(f"响应不是有效的JSON格式，返回原始文本")
                return {'raw_response': response.text}

        except requests.exceptions.RequestException as e:
            logging.error(f"获取第{row_start}-{row_end}条记录失败：{e}")
            return None
        except Exception as e:
            logging.error(f"处理第{row_start}-{row_end}条记录时发生错误：{e}")
            return None
    
    def parse_api_response(self, api_response, batch_start, batch_end):
        """解析API响应数据"""
        try:
            batch_records = []

            if api_response is None:
                return batch_records

            # 如果是原始响应文本
            if 'raw_response' in api_response:
                logging.warning(f"第{batch_start}-{batch_end}条记录返回非JSON响应")
                record = {
                    'batch_start': batch_start,
                    'batch_end': batch_end,
                    'content': api_response['raw_response'][:500] + "..." if len(api_response['raw_response']) > 500 else api_response['raw_response'],
                    'timestamp': datetime.now().isoformat(),
                    'data_type': 'raw_response'
                }
                batch_records.append(record)
                return batch_records

            # 处理JSON响应 - 检查是否直接是数组
            if isinstance(api_response, list):
                logging.info(f"响应是数组格式，包含{len(api_response)}条记录")
                for i, item in enumerate(api_response):
                    if isinstance(item, dict):
                        # 提取文档的关键信息
                        doc_content = item.get('DOCCONTENT', '')
                        doc_title = item.get('DOCTITLE', '')
                        doc_id = item.get('DOCID', '')
                        doc_date = item.get('DOCDATE', '')
                        doc_unit = item.get('DOCUNIT', '')

                        record = {
                            'batch_start': batch_start,
                            'batch_end': batch_end,
                            'item_index': i + 1,
                            'doc_id': doc_id,
                            'doc_title': doc_title,
                            'doc_content': doc_content[:1000] + "..." if len(doc_content) > 1000 else doc_content,  # 截取前1000字符
                            'doc_date': doc_date,
                            'doc_unit': doc_unit,
                            'full_content': doc_content,  # 保存完整内容
                            'raw_data': item,
                            'timestamp': datetime.now().isoformat(),
                            'data_type': 'document'
                        }
                        batch_records.append(record)
                    else:
                        # 如果不是字典，直接保存
                        record = {
                            'batch_start': batch_start,
                            'batch_end': batch_end,
                            'item_index': i + 1,
                            'content': str(item),
                            'raw_data': item,
                            'timestamp': datetime.now().isoformat(),
                            'data_type': 'simple_item'
                        }
                        batch_records.append(record)

                logging.info(f"第{batch_start}-{batch_end}条记录解析完成，获得{len(batch_records)}条记录")
                return batch_records

            # 处理字典格式的JSON响应
            if isinstance(api_response, dict):
                # 检查常见的数据字段
                data_fields = ['data', 'list', 'items', 'records', 'docs', 'documents']
                data_found = False

                for field in data_fields:
                    if field in api_response and api_response[field]:
                        data_list = api_response[field]
                        if isinstance(data_list, list):
                            logging.info(f"在字段'{field}'中找到{len(data_list)}条记录")
                            for i, item in enumerate(data_list):
                                record = {
                                    'batch_start': batch_start,
                                    'batch_end': batch_end,
                                    'item_index': i + 1,
                                    'content': str(item),
                                    'raw_data': item,
                                    'timestamp': datetime.now().isoformat(),
                                    'data_type': 'structured_data'
                                }
                                batch_records.append(record)
                            data_found = True
                            break

                # 如果没有找到标准数据字段，保存整个响应
                if not data_found:
                    logging.info(f"第{batch_start}-{batch_end}条记录：未找到标准数据字段，保存完整响应")
                    record = {
                        'batch_start': batch_start,
                        'batch_end': batch_end,
                        'content': json.dumps(api_response, ensure_ascii=False, indent=2),
                        'raw_data': api_response,
                        'timestamp': datetime.now().isoformat(),
                        'data_type': 'full_response'
                    }
                    batch_records.append(record)

            logging.info(f"第{batch_start}-{batch_end}条记录解析完成，获得{len(batch_records)}条记录")
            return batch_records

        except Exception as e:
            logging.error(f"解析第{batch_start}-{batch_end}条记录失败：{e}")
            return []
    
    def save_to_csv(self, filename='oa_data.csv'):
        """保存数据到CSV文件"""
        if not self.records:
            logging.warning("没有数据可保存")
            return

        try:
            # 获取所有可能的字段名
            all_fieldnames = set()
            for record in self.records:
                all_fieldnames.update(record.keys())

            fieldnames = sorted(list(all_fieldnames))

            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for record in self.records:
                    # 确保所有字段都有值，缺失的用空字符串填充
                    row = {field: record.get(field, '') for field in fieldnames}
                    writer.writerow(row)

            logging.info(f"数据已保存到 {filename}，共{len(self.records)}条记录")

        except Exception as e:
            logging.error(f"保存CSV文件失败：{e}")
    
    def save_to_json(self, filename='oa_data.json'):
        """保存数据到JSON文件"""
        if not self.records:
            logging.warning("没有数据可保存")
            return
        
        try:
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(self.records, jsonfile, ensure_ascii=False, indent=2)
            
            logging.info(f"数据已保存到 {filename}，共{len(self.records)}条记录")
            
        except Exception as e:
            logging.error(f"保存JSON文件失败：{e}")
    
    def scrape(self):
        """主要的爬取方法"""
        logging.info(f"开始爬取数据，目标：{self.total_target}条记录")

        # 首先获取文档总数
        total_docs = self.get_doc_num()
        if total_docs is None:
            logging.error("无法获取文档总数，停止爬取")
            return

        logging.info(f"服务器上共有{total_docs}条文档")

        row_start = 0
        consecutive_failures = 0
        max_consecutive_failures = 5

        while len(self.records) < self.total_target and row_start < total_docs:
            row_end = row_start + self.records_per_batch

            logging.info(f"正在爬取第{row_start+1}-{row_end}条记录...")

            # 获取批次数据
            api_response = self.get_batch_data(row_start, row_end)

            if api_response is None:
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    logging.error(f"连续{max_consecutive_failures}次失败，停止爬取")
                    break

                logging.warning(f"第{row_start+1}-{row_end}条记录获取失败，等待{self.delay * 2}秒后重试...")
                time.sleep(self.delay * 2)
                continue

            # 重置失败计数
            consecutive_failures = 0

            # 解析API响应
            batch_records = self.parse_api_response(api_response, row_start, row_end)

            if not batch_records:
                logging.warning(f"第{row_start+1}-{row_end}条记录没有解析到数据，可能已到达最后一页")
                break

            # 添加到总记录中
            self.records.extend(batch_records)

            logging.info(f"已爬取{len(self.records)}条记录，进度：{len(self.records)/self.total_target*100:.1f}%")

            # 每1000条记录保存一次数据（防止数据丢失）
            if len(self.records) % 1000 == 0:
                self.save_to_csv(f'oa_data_backup_{len(self.records)}.csv')
                self.save_to_json(f'oa_data_backup_{len(self.records)}.json')

            row_start = row_end

            # 延时避免被封
            time.sleep(self.delay)

        # 最终保存
        self.save_to_csv()
        self.save_to_json()

        logging.info(f"爬取完成！总共获取{len(self.records)}条记录")

def main():
    """主函数"""
    scraper = OAScraper()
    
    try:
        scraper.scrape()
    except KeyboardInterrupt:
        logging.info("用户中断爬取")
        # 保存已获取的数据
        scraper.save_to_csv('oa_data_interrupted.csv')
        scraper.save_to_json('oa_data_interrupted.json')
    except Exception as e:
        logging.error(f"爬取过程中发生错误：{e}")
        # 保存已获取的数据
        scraper.save_to_csv('oa_data_error.csv')
        scraper.save_to_json('oa_data_error.json')

if __name__ == "__main__":
    main()
