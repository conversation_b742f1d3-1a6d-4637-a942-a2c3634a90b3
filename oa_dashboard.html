<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>汕头大学OA数据可视化仪表板</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/wordcloud@1.2.2/src/wordcloud2.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .file-upload {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 40px;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: #e8f5e8;
        }

        .upload-icon {
            font-size: 3em;
            color: #3498db;
            margin-bottom: 15px;
        }

        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .kpi-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
        }

        .kpi-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .kpi-label {
            color: #7f8c8d;
            font-size: 1.1em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .charts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 1.4em;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }

        .chart-canvas {
            max-height: 400px;
        }

        .data-table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .search-filter {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .search-input, .filter-select {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .search-input:focus, .filter-select:focus {
            outline: none;
            border-color: #3498db;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e0e0e0;
        }

        .data-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 16px;
            border: 1px solid #3498db;
            background: white;
            color: #3498db;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover,
        .pagination button.active {
            background: #3498db;
            color: white;
        }

        .pagination button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .wordcloud-container {
            height: 400px;
            position: relative;
        }

        @media (max-width: 768px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .search-filter {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>🏛️ 汕头大学OA数据可视化仪表板</h1>
            <p>Professional Data Analytics Dashboard - Powered by Advanced Visualization</p>
        </div>

        <!-- 文件上传区域 -->
        <div class="file-upload">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <h3>拖拽或点击上传JSON数据文件</h3>
                <p>支持格式：JSON (.json)</p>
                <input type="file" id="fileInput" accept=".json" style="display: none;">
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在处理数据，请稍候...</p>
        </div>

        <!-- 主要内容区域 -->
        <div id="mainContent" class="hidden">
            <!-- KPI指标卡片 -->
            <div class="kpi-grid">
                <div class="kpi-card">
                    <div class="kpi-value" id="totalDocs">0</div>
                    <div class="kpi-label">总文档数</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value" id="totalUnits">0</div>
                    <div class="kpi-label">发布单位数</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value" id="avgContentLength">0</div>
                    <div class="kpi-label">平均内容长度</div>
                </div>
                <div class="kpi-card">
                    <div class="kpi-value" id="latestDate">-</div>
                    <div class="kpi-label">最新发布日期</div>
                </div>
            </div>

            <!-- 图表网格 -->
            <div class="charts-grid">
                <!-- 发布单位分布 -->
                <div class="chart-container">
                    <div class="chart-title">📊 发布单位分布</div>
                    <canvas id="unitChart" class="chart-canvas"></canvas>
                </div>

                <!-- 时间趋势 -->
                <div class="chart-container">
                    <div class="chart-title">📈 发布时间趋势</div>
                    <canvas id="timeChart" class="chart-canvas"></canvas>
                </div>

                <!-- 内容长度分布 -->
                <div class="chart-container">
                    <div class="chart-title">📏 内容长度分布</div>
                    <canvas id="lengthChart" class="chart-canvas"></canvas>
                </div>

                <!-- 词云图 -->
                <div class="chart-container">
                    <div class="chart-title">☁️ 关键词云</div>
                    <div id="wordcloud" class="wordcloud-container"></div>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="data-table-container">
                <div class="chart-title">📋 详细数据表格</div>
                
                <!-- 搜索和筛选 -->
                <div class="search-filter">
                    <input type="text" id="searchInput" class="search-input" placeholder="搜索文档标题或内容...">
                    <select id="unitFilter" class="filter-select">
                        <option value="">所有发布单位</option>
                    </select>
                    <select id="dateFilter" class="filter-select">
                        <option value="">所有时间</option>
                        <option value="7">最近7天</option>
                        <option value="30">最近30天</option>
                        <option value="90">最近90天</option>
                    </select>
                </div>

                <!-- 数据表格 -->
                <table class="data-table" id="dataTable">
                    <thead>
                        <tr>
                            <th>文档ID</th>
                            <th>标题</th>
                            <th>发布单位</th>
                            <th>发布人</th>
                            <th>发布日期</th>
                            <th>内容长度</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                    </tbody>
                </table>

                <!-- 分页 -->
                <div class="pagination" id="pagination"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let rawData = [];
        let filteredData = [];
        let currentPage = 1;
        const itemsPerPage = 10;
        let charts = {};

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeUpload();
        });

        // 文件上传初始化
        function initializeUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 点击上传
            uploadArea.addEventListener('click', () => fileInput.click());

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });
        }

        // 处理文件选择
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }

        // 处理文件
        function handleFile(file) {
            if (!file.name.endsWith('.json')) {
                alert('请选择JSON格式的文件！');
                return;
            }

            showLoading();

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    processData(data);
                } catch (error) {
                    alert('JSON文件格式错误：' + error.message);
                    hideLoading();
                }
            };
            reader.readAsText(file);
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('mainContent').classList.add('hidden');
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('mainContent').classList.remove('hidden');
        }

        // 处理数据
        function processData(data) {
            rawData = data;
            filteredData = [...rawData];

            // 更新KPI
            updateKPIs();

            // 创建图表
            createCharts();

            // 更新表格
            updateTable();

            // 初始化筛选器
            initializeFilters();

            hideLoading();
        }

        // 更新KPI指标
        function updateKPIs() {
            const totalDocs = rawData.length;

            // 修正字段映射 - 检查实际的数据结构
            console.log('Sample data:', rawData[0]); // 调试用

            // 尝试多种可能的字段名
            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            // 获取发布单位
            const units = [...new Set(rawData.map(item => {
                return getFieldValue(item, ['SUBCOMPANYNAME', 'doc_unit', 'DOCUNIT', 'unit', 'department']) || '未知';
            }).filter(unit => unit && unit !== '未知'))];
            const totalUnits = units.length;

            // 获取内容长度
            const contentLengths = rawData.map(item => {
                const content = getFieldValue(item, ['DOCCONTENT', 'full_content', 'doc_content', 'content']) || '';
                return content.length;
            });
            const avgContentLength = contentLengths.length > 0 ?
                Math.round(contentLengths.reduce((a, b) => a + b, 0) / contentLengths.length) : 0;

            // 获取日期
            const dates = rawData.map(item => {
                return getFieldValue(item, ['doc_date', 'DOCDATE', 'date', 'publish_date', 'timestamp']);
            }).filter(date => date);

            let latestDate = '-';
            if (dates.length > 0) {
                try {
                    const validDates = dates.map(d => new Date(d)).filter(d => !isNaN(d));
                    if (validDates.length > 0) {
                        latestDate = new Date(Math.max(...validDates)).toLocaleDateString();
                    }
                } catch (e) {
                    console.error('Date parsing error:', e);
                }
            }

            document.getElementById('totalDocs').textContent = totalDocs.toLocaleString();
            document.getElementById('totalUnits').textContent = totalUnits;
            document.getElementById('avgContentLength').textContent = avgContentLength.toLocaleString();
            document.getElementById('latestDate').textContent = latestDate;
        }

        // 创建图表
        function createCharts() {
            createUnitChart();
            createTimeChart();
            createLengthChart();
            createWordCloud();
        }

        // 发布单位分布图
        function createUnitChart() {
            const ctx = document.getElementById('unitChart').getContext('2d');

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            const unitCounts = {};
            rawData.forEach(item => {
                const unit = getFieldValue(item, ['SUBCOMPANYNAME', 'doc_unit', 'DOCUNIT', 'unit', 'department']) || '未知';
                unitCounts[unit] = (unitCounts[unit] || 0) + 1;
            });

            const sortedUnits = Object.entries(unitCounts)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 10); // 取前10个

            if (charts.unitChart) {
                charts.unitChart.destroy();
            }

            charts.unitChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: sortedUnits.map(item => item[0]),
                    datasets: [{
                        data: sortedUnits.map(item => item[1]),
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                            '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
                            '#4BC0C0', '#FF6384'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed * 100) / total).toFixed(1);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // 时间趋势图
        function createTimeChart() {
            const ctx = document.getElementById('timeChart').getContext('2d');

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            const dateCounts = {};
            rawData.forEach(item => {
                const dateValue = getFieldValue(item, ['doc_date', 'DOCDATE', 'date', 'publish_date', 'timestamp']);
                if (dateValue) {
                    try {
                        const date = new Date(dateValue).toISOString().split('T')[0];
                        if (!isNaN(new Date(date))) {
                            dateCounts[date] = (dateCounts[date] || 0) + 1;
                        }
                    } catch (e) {
                        console.error('Date parsing error:', e);
                    }
                }
            });

            const sortedDates = Object.entries(dateCounts)
                .sort((a, b) => new Date(a[0]) - new Date(b[0]))
                .slice(-30); // 最近30天

            if (charts.timeChart) {
                charts.timeChart.destroy();
            }

            // 如果没有日期数据，显示提示
            if (sortedDates.length === 0) {
                ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
                ctx.font = '16px Arial';
                ctx.fillStyle = '#999';
                ctx.textAlign = 'center';
                ctx.fillText('暂无日期数据', ctx.canvas.width / 2, ctx.canvas.height / 2);
                return;
            }

            charts.timeChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: sortedDates.map(item => new Date(item[0]).toLocaleDateString()),
                    datasets: [{
                        label: '发布数量',
                        data: sortedDates.map(item => item[1]),
                        borderColor: '#36A2EB',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#36A2EB',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        }
                    }
                }
            });
        }

        // 内容长度分布图
        function createLengthChart() {
            const ctx = document.getElementById('lengthChart').getContext('2d');

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return '';
            };

            const lengths = rawData.map(item => {
                const content = getFieldValue(item, ['DOCCONTENT', 'full_content', 'doc_content', 'content']);
                return content.length;
            });

            const ranges = [
                { label: '0-500字', min: 0, max: 500 },
                { label: '500-1000字', min: 500, max: 1000 },
                { label: '1000-2000字', min: 1000, max: 2000 },
                { label: '2000-5000字', min: 2000, max: 5000 },
                { label: '5000字以上', min: 5000, max: Infinity }
            ];

            const rangeCounts = ranges.map(range => ({
                label: range.label,
                count: lengths.filter(len => len >= range.min && len < range.max).length
            }));

            if (charts.lengthChart) {
                charts.lengthChart.destroy();
            }

            charts.lengthChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: rangeCounts.map(item => item.label),
                    datasets: [{
                        label: '文档数量',
                        data: rangeCounts.map(item => item.count),
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)'
                        ],
                        borderColor: [
                            'rgba(255, 99, 132, 1)',
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)'
                        ],
                        borderWidth: 2,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0,0,0,0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // 词云图
        function createWordCloud() {
            const container = document.getElementById('wordcloud');
            container.innerHTML = '';

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return '';
            };

            // 提取关键词
            const allText = rawData.map(item => {
                const title = getFieldValue(item, ['DOCSUBJECT', 'doc_title', 'DOCTITLE', 'title']);
                const content = getFieldValue(item, ['DOCCONTENT', 'full_content', 'doc_content', 'content']);
                return title + ' ' + content;
            }).join(' ');

            const words = extractKeywords(allText);

            if (words.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #999; padding: 50px;">暂无关键词数据</p>';
                return;
            }

            // 检查WordCloud是否可用
            if (typeof WordCloud === 'undefined') {
                // 如果WordCloud库未加载，显示简单的词频列表
                const wordList = words.slice(0, 20).map(([word, count]) =>
                    `<span style="font-size: ${Math.min(count * 2 + 12, 24)}px; margin: 5px; color: #${Math.floor(Math.random()*16777215).toString(16)};">${word}(${count})</span>`
                ).join(' ');
                container.innerHTML = `<div style="text-align: center; padding: 20px; line-height: 2;">${wordList}</div>`;
                return;
            }

            // 创建词云
            try {
                WordCloud(container, {
                    list: words,
                    gridSize: Math.round(16 * container.offsetWidth / 1024),
                    weightFactor: function(size) {
                        return Math.pow(size, 2.3) * container.offsetWidth / 1024;
                    },
                    fontFamily: 'Microsoft YaHei, sans-serif',
                    color: function() {
                        const colors = ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'];
                        return colors[Math.floor(Math.random() * colors.length)];
                    },
                    rotateRatio: 0.5,
                    rotationSteps: 2,
                    backgroundColor: 'transparent'
                });
            } catch (e) {
                console.error('WordCloud error:', e);
                // 降级显示
                const wordList = words.slice(0, 20).map(([word, count]) =>
                    `<span style="font-size: ${Math.min(count * 2 + 12, 24)}px; margin: 5px; color: #${Math.floor(Math.random()*16777215).toString(16)};">${word}(${count})</span>`
                ).join(' ');
                container.innerHTML = `<div style="text-align: center; padding: 20px; line-height: 2;">${wordList}</div>`;
            }
        }

        // 提取关键词
        function extractKeywords(text) {
            // 简单的中文关键词提取
            const stopWords = new Set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '他', '她', '它', '我们', '你们', '他们', '这个', '那个', '什么', '怎么', '为什么', '因为', '所以', '但是', '如果', '虽然', '然后', '现在', '已经', '还是', '可以', '应该', '需要', '希望', '觉得', '知道', '认为', '发现', '开始', '继续', '完成', '进行', '实现', '提高', '加强', '推进', '落实', '贯彻', '执行', '实施', '开展', '组织', '参与', '支持', '配合', '协调', '沟通', '交流', '合作', '共同', '一起', '同时', '另外', '此外', '而且', '不仅', '还有', '包括', '涉及', '关于', '对于', '根据', '按照', '依据', '通过', '采用', '利用', '运用', '使用', '建设', '发展', '改革', '创新', '管理', '服务', '工作', '任务', '目标', '计划', '方案', '措施', '办法', '制度', '规定', '要求', '标准', '原则', '基础', '条件', '环境', '情况', '问题', '困难', '挑战', '机遇', '优势', '特点', '特色', '重点', '关键', '核心', '主要', '重要', '必要', '有效', '成功', '优秀', '先进', '科学', '合理', '正确', '及时', '全面', '深入', '具体', '明确', '清楚', '详细', '简单', '复杂', '困难', '容易', '方便', '安全', '稳定', '可靠', '持续', '长期', '短期', '当前', '未来', '过去', '历史', '传统', '现代', '新的', '老的', '大的', '小的', '多的', '少的', '高的', '低的', '快的', '慢的', '好的', '坏的', '对的', '错的', '真的', '假的', '实际', '理论', '实践', '经验', '教训', '成果', '效果', '影响', '作用', '意义', '价值', '利益', '损失', '风险', '机会', '可能', '必须', '应当', '能够', '愿意', '希望', '期待', '担心', '害怕', '喜欢', '讨厌', '同意', '反对', '支持', '反对', '赞成', '反对', '肯定', '否定', '确定', '不确定', '清楚', '不清楚', '明白', '不明白', '理解', '不理解', '接受', '拒绝', '同意', '不同意', '满意', '不满意', '高兴', '不高兴', '开心', '不开心', '快乐', '痛苦', '幸福', '不幸', '成功', '失败', '胜利', '失败', '正确', '错误', '对', '错', '是', '不是', '有', '没有', '存在', '不存在', '出现', '消失', '增加', '减少', '提高', '降低', '上升', '下降', '前进', '后退', '发展', '倒退', '进步', '退步', '改善', '恶化', '好转', '恶化', '变化', '不变', '稳定', '不稳定', '正常', '异常', '一般', '特殊', '普通', '特别', '常见', '罕见', '经常', '偶尔', '总是', '从不', '永远', '暂时', '临时', '长久', '短暂', '瞬间', '持久', '永恒', '有限', '无限', '全部', '部分', '整体', '局部', '完整', '不完整', '完全', '不完全', '绝对', '相对', '肯定', '可能', '也许', '大概', '估计', '预计', '预测', '预期', '期望', '希望', '愿望', '梦想', '理想', '目标', '计划', '安排', '准备', '开始', '结束', '完成', '继续', '停止', '暂停', '中断', '恢复', '重新', '再次', '又', '还', '仍然', '依然', '始终', '一直', '从来', '曾经', '以前', '以后', '之前', '之后', '当时', '现在', '将来', '过去', '未来', '今天', '昨天', '明天', '今年', '去年', '明年', '这里', '那里', '哪里', '到处', '处处', '各处', '某处', '此处', '彼处', '内部', '外部', '上面', '下面', '前面', '后面', '左边', '右边', '中间', '旁边', '附近', '远处', '近处', '周围', '四周', '各地', '全国', '全省', '全市', '全县', '全区', '全校', '全院', '全系', '全班', '全组', '全队', '全体', '大家', '各位', '同志', '同学', '老师', '学生', '领导', '群众', '人民', '公民', '居民', '村民', '市民', '网民', '用户', '客户', '顾客', '消费者', '生产者', '经营者', '管理者', '服务者', '工作者', '从业者', '专业者', '技术者', '研究者', '学者', '专家', '教授', '博士', '硕士', '学士', '高中', '初中', '小学', '幼儿园', '大学', '学院', '学校', '机构', '组织', '单位', '部门', '公司', '企业', '工厂', '商店', '医院', '银行', '政府', '国家', '社会', '集体', '个人', '家庭', '亲戚', '朋友', '同事', '邻居', '陌生人', '熟人', '生人', '客人', '主人', '老板', '员工', '下属', '上级', '同级', '平级', '高级', '中级', '初级', '资深', '新手', '老手', '专业', '业余', '正式', '非正式', '公开', '秘密', '透明', '不透明', '清晰', '模糊', '明显', '隐藏', '显示', '隐藏', '表现', '表达', '表示', '说明', '解释', '介绍', '描述', '叙述', '讲述', '告诉', '通知', '通报', '报告', '汇报', '反映', '反馈', '回应', '答复', '回答', '询问', '提问', '质疑', '怀疑', '相信', '信任', '依赖', '依靠', '依据', '根据', '基于', '按照', '遵循', '遵守', '违反', '违背', '符合', '不符合', '一致', '不一致', '相同', '不同', '相似', '不似', '像', '不像', '等于', '不等于', '大于', '小于', '多于', '少于', '超过', '不足', '达到', '未达到', '实现', '未实现', '完成', '未完成', '成功', '失败', '有效', '无效', '正确', '错误', '准确', '不准确', '精确', '不精确', '真实', '虚假', '客观', '主观', '公正', '不公正', '公平', '不公平', '合理', '不合理', '合法', '非法', '正当', '不正当', '适当', '不适当', '合适', '不合适', '恰当', '不恰当', '及时', '不及时', '准时', '迟到', '早到', '按时', '延时', '提前', '推迟', '延期', '取消', '暂停', '中止', '终止', '结束', '开始', '启动', '发动', '发起', '发生', '产生', '造成', '导致', '引起', '带来', '形成', '建立', '创建', '设立', '成立', '建设', '构建', '打造', '制作', '生产', '制造', '加工', '处理', '操作', '使用', '利用', '运用', '应用', '采用', '选用', '使用', '运行', '运转', '工作', '运作', '操作', '控制', '管理', '监督', '检查', '审查', '审核', '评估', '评价', '考核', '测试', '检测', '测量', '计算', '统计', '分析', '研究', '调查', '了解', '掌握', '熟悉', '学习', '培训', '教育', '指导', '帮助', '支持', '协助', '配合', '合作', '协作', '联合', '团结', '统一', '一致', '共同', '一起', '同时', '并且', '而且', '以及', '还有', '另外', '此外', '除了', '包括', '含有', '涉及', '关于', '对于', '针对', '面对', '面向', '朝向', '向着', '往', '到', '从', '由', '被', '让', '使', '令', '叫', '请', '要求', '需要', '必须', '应该', '可以', '能够', '会', '将', '要', '想', '希望', '期望', '打算', '计划', '准备', '决定', '选择', '确定', '肯定', '否定', '同意', '反对', '支持', '反对', '赞成', '反对', '接受', '拒绝', '承认', '否认', '确认', '否认', '证实', '证明', '表明', '显示', '表示', '说明', '解释', '阐述', '论述', '讨论', '谈论', '议论', '评论', '批评', '表扬', '赞扬', '称赞', '夸奖', '鼓励', '激励', '促进', '推动', '推进', '发展', '提高', '改善', '完善', '优化', '强化', '加强', '增强', '提升', '升级', '更新', '改进', '改革', '变革', '创新', '革新', '改变', '转变', '变化', '发展', '进步', '前进', '向前', '往前', '朝前', '向上', '往上', '朝上', '向下', '往下', '朝下', '向左', '往左', '朝左', '向右', '往右', '朝右', '向内', '往内', '朝内', '向外', '往外', '朝外', '进入', '进去', '进来', '出去', '出来', '离开', '离去', '回来', '回去', '返回', '回到', '到达', '抵达', '来到', '去到', '走到', '跑到', '飞到', '游到', '爬到', '跳到', '滚到', '滑到', '移到', '搬到', '运到', '送到', '带到', '拿到', '取到', '得到', '获得', '收到', '接到', '买到', '卖到', '租到', '借到', '还到', '给到', '交到', '传到', '递到', '送到', '寄到', '邮到', '发到', '打到', '拨到', '呼到', '叫到', '喊到', '说到', '讲到', '谈到', '提到', '想到', '看到', '听到', '闻到', '尝到', '摸到', '感到', '觉到', '察到', '发现', '找到', '遇到', '碰到', '撞到', '击到', '打到', '踢到', '推到', '拉到', '拖到', '抬到', '举到', '放到', '摆到', '排到', '列到', '站到', '坐到', '躺到', '睡到', '醒到', '起到', '倒到', '跌到', '摔到', '掉到', '落到', '降到', '升到', '涨到', '跌到', '增到', '减到', '加到', '减到', '乘到', '除到', '等到', '算到', '数到', '量到', '称到', '重到', '轻到', '大到', '小到', '长到', '短到', '高到', '低到', '深到', '浅到', '厚到', '薄到', '宽到', '窄到', '粗到', '细到', '胖到', '瘦到', '老到', '少到', '新到', '旧到', '好到', '坏到', '美到', '丑到', '亮到', '暗到', '明到', '暗到', '清到', '浊到', '净到', '脏到', '干到', '湿到', '热到', '冷到', '温到', '凉到', '暖到', '寒到', '酸到', '甜到', '苦到', '辣到', '咸到', '淡到', '香到', '臭到', '响到', '静到', '快到', '慢到', '急到', '缓到', '紧到', '松到', '硬到', '软到', '滑到', '粗到', '光到', '毛到', '平到', '凸到', '凹到', '直到', '弯到', '圆到', '方到', '尖到', '钝到', '锐到', '利到', '钝到', '锋到', '刃到', '边到', '角到', '面到', '线到', '点到', '中到', '心到', '核到', '本到', '根到', '源到', '头到', '尾到', '端到', '顶到', '底到', '表到', '里到', '内到', '外到', '前到', '后到', '左到', '右到', '上到', '下到', '东到', '西到', '南到', '北到', '中到', '央到', '间到', '际到', '边到', '缘到', '沿到', '岸到', '滨到', '畔到', '旁到', '侧到', '侧到', '面到', '方到', '向到', '处到', '所到', '地到', '点到', '位到', '置到', '址到', '场到', '域到', '区到', '带到', '线到', '路到', '道到', '街到', '巷到', '弄到', '胡到', '同到', '里到', '村到', '镇到', '县到', '市到', '省到', '国到', '洲到', '球到', '界到', '世到', '人到', '间到', '宇到', '宙到', '空到', '天到', '地到', '海到', '洋到', '河到', '江到', '湖到', '池到', '塘到', '井到', '泉到', '溪到', '流到', '水到', '火到', '土到', '木到', '金到', '石到', '山到', '岭到', '峰到', '坡到', '谷到', '沟到', '坑到', '洞到', '穴到', '孔到', '口到', '门到', '窗到', '墙到', '壁到', '顶到', '底到', '层到', '楼到', '房到', '屋到', '室到', '厅到', '堂到', '院到', '园到', '场到', '馆到', '店到', '铺到', '摊到', '台到', '桌到', '椅到', '床到', '柜到', '箱到', '包到', '袋到', '盒到', '瓶到', '罐到', '杯到', '碗到', '盘到', '碟到', '勺到', '筷到', '刀到', '叉到', '锅到', '锅到', '炉到', '灶到', '炊到', '具到', '器到', '械到', '机到', '设到', '备到', '施到', '工到', '具到', '材到', '料到', '物到', '品到', '货到', '商到', '产到', '制到', '造到', '作到', '做到', '干到', '活到', '事到', '情到', '况到', '形到', '状到', '态到', '势到', '局到', '面到', '象到', '现到', '实到', '际到', '真到', '假到', '虚到', '空到', '无到', '有到', '存到', '在到', '生到', '死到', '活到', '命到', '身到', '体到', '心到', '灵到', '魂到', '神到', '精到', '气到', '力到', '能到', '量到', '度到', '速到', '率到', '效到', '果到', '用到', '功到', '能到', '力到', '智到', '慧到', '识到', '知到', '学到', '问到', '题到', '答到', '案到', '法到', '方到', '式到', '样到', '种到', '类到', '别到', '分到', '组到', '队到', '群到', '众到', '人到', '员到', '手到', '工到', '师到', '匠到', '家到', '者到', '人到', '士到', '生到', '民到', '众到', '群到', '体到', '个到', '单到', '独到', '孤到', '寂到', '静到', '安到', '全到', '危到', '险到', '难到', '易到', '简到', '单到', '复到', '杂到', '乱到', '序到', '理到', '条到', '规到', '律到', '则到', '制到', '度到', '法到', '规到', '定到', '例到', '案到', '件到', '事到', '物到', '东到', '西到', '南到', '北到', '中到', '外到', '内到', '里到', '表到', '面到', '层到', '级到', '等到', '次到', '第到', '一到', '二到', '三到', '四到', '五到', '六到', '七到', '八到', '九到', '十到', '百到', '千到', '万到', '亿到', '兆到', '京到', '垓到', '秭到', '穰到', '沟到', '涧到', '正到', '载到', '极到', '恒到', '河到', '沙到', '阿到', '僧到', '祇到', '那到', '由到', '他到', '不到', '可到', '思到', '议到', '无到', '量到', '大到', '数到']);

            // 分词（简单按字符分割）
            const words = text.match(/[\u4e00-\u9fa5]{2,}/g) || [];

            // 统计词频
            const wordCount = {};
            words.forEach(word => {
                if (!stopWords.has(word) && word.length >= 2) {
                    wordCount[word] = (wordCount[word] || 0) + 1;
                }
            });

            // 转换为词云格式并排序
            return Object.entries(wordCount)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 100)
                .map(([word, count]) => [word, count]);
        }

        // 初始化筛选器
        function initializeFilters() {
            const unitFilter = document.getElementById('unitFilter');
            const searchInput = document.getElementById('searchInput');
            const dateFilter = document.getElementById('dateFilter');

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            // 填充发布单位选项
            const units = [...new Set(rawData.map(item => {
                return getFieldValue(item, ['SUBCOMPANYNAME', 'doc_unit', 'DOCUNIT', 'unit', 'department']) || '未知';
            }).filter(unit => unit && unit !== '未知'))];

            unitFilter.innerHTML = '<option value="">所有发布单位</option>';
            units.forEach(unit => {
                const option = document.createElement('option');
                option.value = unit;
                option.textContent = unit;
                unitFilter.appendChild(option);
            });

            // 绑定事件
            searchInput.addEventListener('input', applyFilters);
            unitFilter.addEventListener('change', applyFilters);
            dateFilter.addEventListener('change', applyFilters);
        }

        // 应用筛选
        function applyFilters() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const selectedUnit = document.getElementById('unitFilter').value;
            const selectedDateRange = document.getElementById('dateFilter').value;

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            filteredData = rawData.filter(item => {
                // 搜索筛选
                const title = getFieldValue(item, ['DOCSUBJECT', 'doc_title', 'DOCTITLE', 'title']) || '';
                const content = getFieldValue(item, ['DOCCONTENT', 'full_content', 'doc_content', 'content']) || '';
                const searchMatch = !searchTerm ||
                    title.toLowerCase().includes(searchTerm) ||
                    content.toLowerCase().includes(searchTerm);

                // 单位筛选
                const unit = getFieldValue(item, ['SUBCOMPANYNAME', 'doc_unit', 'DOCUNIT', 'unit', 'department']) || '未知';
                const unitMatch = !selectedUnit || unit === selectedUnit;

                // 日期筛选
                let dateMatch = true;
                if (selectedDateRange) {
                    const dateValue = getFieldValue(item, ['doc_date', 'DOCDATE', 'date', 'publish_date', 'timestamp']);
                    if (dateValue) {
                        try {
                            const itemDate = new Date(dateValue);
                            const now = new Date();
                            const daysAgo = parseInt(selectedDateRange);
                            const cutoffDate = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
                            dateMatch = itemDate >= cutoffDate;
                        } catch (e) {
                            dateMatch = true; // 如果日期解析失败，不过滤
                        }
                    }
                }

                return searchMatch && unitMatch && dateMatch;
            });

            currentPage = 1;
            updateTable();
        }

        // 更新表格
        function updateTable() {
            const tableBody = document.getElementById('tableBody');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const pageData = filteredData.slice(startIndex, endIndex);

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            tableBody.innerHTML = '';

            pageData.forEach((item, index) => {
                const docId = getFieldValue(item, ['DOCID', 'doc_id', 'id']) || (startIndex + index + 1);
                const docTitle = getFieldValue(item, ['DOCSUBJECT', 'doc_title', 'DOCTITLE', 'title']) || '无标题';
                const docUnit = getFieldValue(item, ['SUBCOMPANYNAME', 'doc_unit', 'DOCUNIT', 'unit', 'department']) || '未知';
                const docDate = getFieldValue(item, ['DOCDATE', 'doc_date', 'date', 'publish_date', 'timestamp']);
                const content = getFieldValue(item, ['DOCCONTENT', 'full_content', 'doc_content', 'content']) || '';
                const author = getFieldValue(item, ['LASTNAME', 'author', 'publisher']) || '';

                let formattedDate = '-';
                if (docDate) {
                    try {
                        formattedDate = new Date(docDate).toLocaleDateString();
                    } catch (e) {
                        formattedDate = docDate.toString().substring(0, 10);
                    }
                }

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${docId}</td>
                    <td title="${docTitle}">${truncateText(docTitle, 30)}</td>
                    <td>${docUnit}</td>
                    <td>${author}</td>
                    <td>${formattedDate}</td>
                    <td>${content.length.toLocaleString()}</td>
                    <td>
                        <button onclick="viewDocument(${startIndex + index})" style="padding: 5px 10px; background: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer;">查看</button>
                    </td>
                `;
                tableBody.appendChild(row);
            });

            updatePagination();
        }

        // 截断文本
        function truncateText(text, maxLength) {
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        // 更新分页
        function updatePagination() {
            const pagination = document.getElementById('pagination');
            const totalPages = Math.ceil(filteredData.length / itemsPerPage);

            pagination.innerHTML = '';

            // 上一页
            const prevBtn = document.createElement('button');
            prevBtn.textContent = '上一页';
            prevBtn.disabled = currentPage === 1;
            prevBtn.onclick = () => {
                if (currentPage > 1) {
                    currentPage--;
                    updateTable();
                }
            };
            pagination.appendChild(prevBtn);

            // 页码
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);

            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.textContent = i;
                pageBtn.className = i === currentPage ? 'active' : '';
                pageBtn.onclick = () => {
                    currentPage = i;
                    updateTable();
                };
                pagination.appendChild(pageBtn);
            }

            // 下一页
            const nextBtn = document.createElement('button');
            nextBtn.textContent = '下一页';
            nextBtn.disabled = currentPage === totalPages;
            nextBtn.onclick = () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    updateTable();
                }
            };
            pagination.appendChild(nextBtn);

            // 显示统计信息
            const info = document.createElement('span');
            info.style.marginLeft = '20px';
            info.style.color = '#7f8c8d';
            info.textContent = `共 ${filteredData.length} 条记录，第 ${currentPage} / ${totalPages} 页`;
            pagination.appendChild(info);
        }

        // 查看文档详情
        function viewDocument(index) {
            const doc = filteredData[index];
            if (!doc) {
                alert('文档未找到');
                return;
            }

            const getFieldValue = (item, fieldNames) => {
                for (let field of fieldNames) {
                    if (item[field] !== undefined && item[field] !== null && item[field] !== '') {
                        return item[field];
                    }
                }
                return null;
            };

            const docId = getFieldValue(doc, ['DOCID', 'doc_id', 'id']) || '-';
            const docTitle = getFieldValue(doc, ['DOCSUBJECT', 'doc_title', 'DOCTITLE', 'title']) || '无标题';
            const docUnit = getFieldValue(doc, ['SUBCOMPANYNAME', 'doc_unit', 'DOCUNIT', 'unit', 'department']) || '未知';
            const docDate = getFieldValue(doc, ['DOCDATE', 'doc_date', 'date', 'publish_date', 'timestamp']);
            const content = getFieldValue(doc, ['DOCCONTENT', 'full_content', 'doc_content', 'content']) || '无内容';
            const author = getFieldValue(doc, ['LASTNAME', 'author', 'publisher']) || '未知';

            let formattedDate = '-';
            if (docDate) {
                try {
                    formattedDate = new Date(docDate).toLocaleDateString();
                } catch (e) {
                    formattedDate = docDate.toString();
                }
            }

            // 创建模态框
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                z-index: 1000;
                display: flex;
                justify-content: center;
                align-items: center;
                padding: 20px;
            `;

            const contentDiv = document.createElement('div');
            contentDiv.style.cssText = `
                background: white;
                border-radius: 15px;
                padding: 30px;
                max-width: 800px;
                max-height: 80vh;
                overflow-y: auto;
                position: relative;
            `;

            contentDiv.innerHTML = `
                <button onclick="this.closest('.modal').remove()" style="position: absolute; top: 15px; right: 15px; background: #e74c3c; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer;">×</button>
                <h2 style="color: #2c3e50; margin-bottom: 20px;">${docTitle}</h2>
                <div style="margin-bottom: 15px; color: #7f8c8d;">
                    <strong>文档ID:</strong> ${docId} |
                    <strong>发布单位:</strong> ${docUnit} |
                    <strong>发布人:</strong> ${author} |
                    <strong>发布日期:</strong> ${formattedDate}
                </div>
                <div style="line-height: 1.6; color: #333;">
                    ${content.replace(/\n/g, '<br>')}
                </div>
            `;

            modal.className = 'modal';
            modal.appendChild(contentDiv);
            document.body.appendChild(modal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
        }
    </script>
</body>
</html>
